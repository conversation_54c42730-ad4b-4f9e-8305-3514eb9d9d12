<template>
  <div class="about">
    <h1>关于页面</h1>
    <p>这是关于页面的内容</p>
    <div class="navigation">
      <router-link to="/">返回首页</router-link>
      <router-link to="/media">媒体列表</router-link>
    </div>
  </div>
</template>

<script setup>

</script>

<style scoped lang="less">
.about {
  padding: 20px;
  text-align: center;
}

h1 {
  color: #42b883;
  margin-bottom: 20px;
}

p {
  font-size: 16px;
  margin-bottom: 20px;
}

a {
  color: #42b883;
  text-decoration: none;
  padding: 8px 16px;
  border: 1px solid #42b883;
  border-radius: 4px;
  transition: all 0.3s;
}

a:hover {
  background-color: #42b883;
  color: white;
}
</style>
