import { createRouter, createWebHistory } from 'vue-router'
import Home from '../views/Home.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home
  },
  {
    path: '/about',
    name: 'About',
    // 路由懒加载
    component: () => import('../views/About.vue')
  },
  {
    path: '/media',
    name: 'MediaList',
    // 路由懒加载
    component: () => import('../views/MediaList.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
