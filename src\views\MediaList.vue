<template>
  <div class="media-list">
    <div class="header">
      <h1>媒体列表</h1>
      <button @click="refreshData" class="refresh-btn" :disabled="loading">
        {{ loading ? '刷新中...' : '刷新' }}
      </button>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading">
      <div class="spinner"></div>
      <p>正在加载媒体数据...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error">
      <div class="error-icon">⚠️</div>
      <h3>加载失败</h3>
      <p>{{ error.message }}</p>
      <button @click="fetchMediaList" class="retry-btn">重试</button>
    </div>

    <!-- 数据展示 -->
    <div v-else-if="mediaList.length > 0" class="media-grid">
      <div 
        v-for="media in mediaList" 
        :key="media.id" 
        class="media-card"
        @click="viewMediaDetail(media)"
      >
        <div class="media-thumbnail">
          <img
            v-if="media.thumbnail_path || (media.file_type === 'image' && media.url)"
            :src="media.thumbnail_path || media.url"
            :alt="media.original_name"
            @error="handleImageError"
          />
          <div v-else class="no-image">
            {{ media.file_type === 'video' ? '🎬' : '📁' }}
          </div>
        </div>
        <div class="media-info">
          <h3 class="media-title">{{ media.original_name || media.filename || '未命名' }}</h3>
          <p class="media-description">{{ media.mime_type || '暂无描述' }}</p>
          <div class="media-meta">
            <span class="media-type">{{ media.file_type || '未知类型' }}</span>
            <span class="media-size">{{ formatFileSize(media.file_size) }}</span>
          </div>
          <div class="media-date">
            {{ formatDate(media.created_at) }}
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty">
      <div class="empty-icon">📂</div>
      <h3>暂无媒体数据</h3>
      <p>还没有上传任何媒体文件</p>
    </div>

    <!-- 导航链接 -->
    <div class="navigation">
      <router-link to="/">返回首页</router-link>
      <router-link to="/about">关于页面</router-link>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { mediaApi } from '../api'

// 响应式数据
const mediaList = ref([])
const loading = ref(false)
const error = ref(null)

// 获取媒体列表
const fetchMediaList = async () => {
  loading.value = true
  error.value = null

  try {
    const response = await mediaApi.getMediaList()
    console.log('API 响应:', response)

    // 根据实际 API 响应结构解析数据
    if (response && response.data && Array.isArray(response.data.files)) {
      mediaList.value = response.data.files
    } else if (Array.isArray(response)) {
      mediaList.value = response
    } else {
      mediaList.value = []
    }

    console.log('媒体列表获取成功:', mediaList.value)
  } catch (err) {
    error.value = err
    console.error('获取媒体列表失败:', err)
  } finally {
    loading.value = false
  }
}

// 刷新数据
const refreshData = () => {
  fetchMediaList()
}

// 查看媒体详情
const viewMediaDetail = (media) => {
  console.log('查看媒体详情:', media)
  // 这里可以跳转到详情页面或显示详情弹窗
}

// 处理图片加载错误
const handleImageError = (event) => {
  event.target.style.display = 'none'
  event.target.parentElement.innerHTML = '<div class="no-image">🖼️</div>'
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (!bytes) return '未知大小'
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '未知时间'
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN')
}

// 组件挂载时获取数据
onMounted(() => {
  fetchMediaList()
})
</script>

<style scoped lang="less">
.media-list {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  
  h1 {
    color: #42b883;
    margin: 0;
  }
  
  .refresh-btn {
    padding: 8px 16px;
    background-color: #42b883;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
    
    &:hover:not(:disabled) {
      background-color: #369870;
    }
    
    &:disabled {
      background-color: #ccc;
      cursor: not-allowed;
    }
  }
}

.loading {
  text-align: center;
  padding: 60px 20px;
  
  .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #42b883;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
  }
  
  p {
    color: #666;
    font-size: 16px;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error {
  text-align: center;
  padding: 60px 20px;
  
  .error-icon {
    font-size: 48px;
    margin-bottom: 20px;
  }
  
  h3 {
    color: #e74c3c;
    margin-bottom: 10px;
  }
  
  p {
    color: #666;
    margin-bottom: 20px;
  }
  
  .retry-btn {
    padding: 10px 20px;
    background-color: #e74c3c;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    
    &:hover {
      background-color: #c0392b;
    }
  }
}

.media-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.media-card {
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.2s, box-shadow 0.2s;
  cursor: pointer;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.media-thumbnail {
  height: 200px;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .no-image {
    font-size: 48px;
    color: #ccc;
  }
}

.media-info {
  padding: 15px;
  
  .media-title {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .media-description {
    margin: 0 0 12px 0;
    font-size: 14px;
    color: #666;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .media-meta {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    
    span {
      font-size: 12px;
      color: #999;
      background-color: #f0f0f0;
      padding: 2px 6px;
      border-radius: 3px;
    }
  }
  
  .media-date {
    font-size: 12px;
    color: #999;
  }
}

.empty {
  text-align: center;
  padding: 80px 20px;
  
  .empty-icon {
    font-size: 64px;
    margin-bottom: 20px;
  }
  
  h3 {
    color: #666;
    margin-bottom: 10px;
  }
  
  p {
    color: #999;
  }
}

.navigation {
  text-align: center;
  margin-top: 40px;
  
  a {
    color: #42b883;
    text-decoration: none;
    padding: 8px 16px;
    margin: 0 10px;
    border: 1px solid #42b883;
    border-radius: 4px;
    transition: all 0.3s;
    
    &:hover {
      background-color: #42b883;
      color: white;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .media-grid {
    grid-template-columns: 1fr;
  }
  
  .header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
}
</style>
