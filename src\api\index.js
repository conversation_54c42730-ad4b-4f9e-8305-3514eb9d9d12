import axios from 'axios'

// 创建 axios 实例
const api = axios.create({
  baseURL: 'http://localhost:3000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 可以在这里添加 token 等认证信息
    console.log('发送请求:', config.method?.toUpperCase(), config.url)
    return config
  },
  (error) => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    console.log('响应成功:', response.status, response.config.url)
    return response.data
  },
  (error) => {
    console.error('响应错误:', error.response?.status, error.message)
    
    // 统一错误处理
    const errorMessage = error.response?.data?.message || error.message || '请求失败'
    
    // 根据状态码处理不同错误
    switch (error.response?.status) {
      case 400:
        console.error('请求参数错误')
        break
      case 401:
        console.error('未授权，请重新登录')
        break
      case 403:
        console.error('拒绝访问')
        break
      case 404:
        console.error('请求资源不存在')
        break
      case 500:
        console.error('服务器内部错误')
        break
      default:
        console.error('网络错误')
    }
    
    return Promise.reject({
      status: error.response?.status,
      message: errorMessage,
      originalError: error
    })
  }
)

// 媒体相关 API
export const mediaApi = {
  // 获取媒体列表
  getMediaList: () => {
    return api.get('/media')
  },
  
  // 根据 ID 获取媒体详情
  getMediaById: (id) => {
    return api.get(`/media/${id}`)
  },
  
  // 创建媒体
  createMedia: (data) => {
    return api.post('/media', data)
  },
  
  // 更新媒体
  updateMedia: (id, data) => {
    return api.put(`/media/${id}`, data)
  },
  
  // 删除媒体 	/api/media/:id
  deleteMedia: (id) => {
    return api.delete(`/media/${id}`)
  }
}

// 导出 axios 实例供其他地方使用
export default api
