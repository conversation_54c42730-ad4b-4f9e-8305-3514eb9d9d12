<template>
  <div class="home">
    <h1>首页</h1>
    <p>欢迎来到 Vue 应用首页</p>
    <nav>
      <router-link to="/about">关于页面</router-link>
    </nav>
  </div>
</template>

<script setup>

</script>

<style scoped lang="less">
.home {
  padding: 20px;
  text-align: center;
}

h1 {
  color: #42b883;
  margin-bottom: 20px;
}

p {
  font-size: 16px;
  margin-bottom: 20px;
}

nav {
  margin-top: 20px;
}

a {
  color: #42b883;
  text-decoration: none;
  padding: 8px 16px;
  border: 1px solid #42b883;
  border-radius: 4px;
  transition: all 0.3s;
}

a:hover {
  background-color: #42b883;
  color: white;
}
</style>