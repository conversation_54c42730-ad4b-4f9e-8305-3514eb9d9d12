<template>
  <div class="main">
    <!-- nav -->
    <div class="nav">
      <ul class="nav-list1">
        <li>苹果</li>
        <li>香蕉</li>
        <li>橘子</li>
        <li>香蕉</li>
        <li>橘子</li>
        <li>香蕉</li>
        <li>橘子</li>
        <li>香蕉</li>
      </ul>
      <ul class="nav-list2">
        <li>登录</li>
        <li>注册</li>
      </ul>
    </div>
    <div class="container">
      <!-- nav-left -->
      <div class="nav-left">
        <ul>
          <li>
            品牌
          </li>
          <li>
            手机
          </li>
          <li>
            电脑
          </li>
          <li>
            相机
          </li>
          <li>
            耳机
          </li>
          <li>
            保护套
          </li>
          <li>
            配件
          </li>
          <li>
            配件
          </li>
        </ul>
      </div>
      <!-- content -->
      <div class="content">
        <h1>Hello, {{ msg }}!</h1>
      </div>
    </div>

  </div>
</template>

<script setup>
import { ref } from 'vue'

const msg = ref('Vite + Vue')

</script>

<style scoped>
* {
  margin: 0;
  padding: 0;
  list-style: none;
  text-decoration: none;
  box-sizing: border-box;
}

.main {
  width: 100%;
  height: 100vh;
  display: flex;
  /* 开启flex布局 */
  flex-direction: column;
  /* 子元素垂直排列 */
}

.nav {
  display: flex;
  justify-content: space-between;
  background-color: aqua;
  /* 高度由padding:12px撑开，无需额外设置height */
}

.nav-list1 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  gap: 30px;
  /* 可选：增加li之间的间距，避免挤在一起 */
}

.nav-list2 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  gap: 30px;
  /* 可选：增加登录/注册间距 */
}

.container {
  display: flex;
  flex: 1;
  /* 自动占据剩余高度 */
}

.nav-left {
  flex: 1;
  /* 3份比例 */
  background-color: #f5f5f5;
  padding: 16px;
}
.nav-left ul li {
  margin-bottom: 30px; 
  border-bottom: 1px solid #000;

}

/* 鼠标悬停状态 */
.nav-left ul li:hover {
  background-color: bisque;
}


.content {
  flex: 9;
  /* 7份比例 */
  background-color: #ffffff;
  padding: 16px;
}
</style>
